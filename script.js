document.addEventListener('DOMContentLoaded', () => {

    // Initialize Material Design components
    if (typeof componentHandler !== 'undefined') {
        componentHandler.upgradeDom();
    }

    // --- Language and Translations ---
    const translations = {
        en: {
            mainTitle: "CDP Idea Tracking System", mainSubtitle: "Powering Innovation, Together.",
            formTitle: "Member Login", userIdLabel: "User ID", passwordLabel: "Password",
            placeholderUserId: "Enter your user ID", placeholderPassword: "Enter your password",
            rememberMe: "Remember me", forgotPassword: "Forgot Password?", loginButton: "Login",
            helplineTitle: "Helpline", copyright: "Copyright © 2025 Quest Informatics Pvt Ltd. All rights reserved.",
            errorBothFields: "Please enter both User ID and Password.", loginSuccess: "Login successful! Redirecting...",
        },
        es: {
            mainTitle: "Sistema de Seguimiento de Ideas CDP", mainSubtitle: "Impulsando la Innovación, Juntos.",
            formTitle: "Acceso de Miembros", userIdLabel: "ID de Usuario", passwordLabel: "Contraseña",
            placeholderUserId: "Ingrese su ID de usuario", placeholderPassword: "Ingrese su contraseña",
            rememberMe: "Recuérdame", forgotPassword: "¿Olvidaste tu Contraseña?", loginButton: "Acceder",
            helplineTitle: "Línea de Ayuda", copyright: "Copyright © 2025 Quest Informatics Pvt Ltd. Todos los derechos reservados.",
            errorBothFields: "Por favor, ingrese el ID de usuario y la contraseña.", loginSuccess: "¡Inicio de sesión exitoso! Redirigiendo...",
        },
        de: {
            mainTitle: "CDP Ideen-Tracking-System", mainSubtitle: "Innovation gemeinsam vorantreiben.",
            formTitle: "Mitglieder-Login", userIdLabel: "Benutzer-ID", passwordLabel: "Passwort",
            placeholderUserId: "Geben Sie Ihre Benutzer-ID ein", placeholderPassword: "Geben Sie Ihr Passwort ein",
            rememberMe: "Angemeldet bleiben", forgotPassword: "Passwort vergessen?", loginButton: "Anmelden",
            helplineTitle: "Helpline", copyright: "Copyright © 2025 Quest Informatics Pvt Ltd. Alle Rechte vorbehalten.",
            errorBothFields: "Bitte geben Sie Benutzer-ID und Passwort ein.", loginSuccess: "Anmeldung erfolgreich! Weiterleitung...",
        }
    };

    const languageSwitcher = document.getElementById('language-switcher');
    let currentLang = 'en';

    function setLanguage(lang) {
        currentLang = lang;
        document.querySelectorAll('[data-lang-key]').forEach(elem => {
            const key = elem.getAttribute('data-lang-key');
            const translation = translations[lang][key];

            // Handle placeholders for input elements
            if (key.startsWith('placeholder')) {
                elem.setAttribute('placeholder', translation);
                // Update Material Design textfield if needed
                const parentTextfield = elem.closest('.mdl-textfield');
                if (parentTextfield && parentTextfield.MaterialTextfield) {
                    parentTextfield.MaterialTextfield.checkDirty();
                }
            } else {
                elem.textContent = translation || elem.textContent;
            }
        });

        // Update input placeholders directly
        const userIdInput = document.getElementById('user-id');
        const passwordInput = document.getElementById('password');

        if (userIdInput) {
            userIdInput.placeholder = translations[lang].placeholderUserId;
        }
        if (passwordInput) {
            passwordInput.placeholder = translations[lang].placeholderPassword;
        }

        // Refresh Material Design components after language change
        if (typeof componentHandler !== 'undefined') {
            componentHandler.upgradeDom();
        }
    }

    languageSwitcher.addEventListener('change', (e) => setLanguage(e.target.value));

    // --- Theme Management ---
    const themeToggle = document.getElementById('theme-toggle');
    const docElement = document.documentElement;

    // Set initial theme based on localStorage or system preference
    if (localStorage.getItem('theme') === 'dark' || (!('theme' in localStorage) && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        docElement.classList.add('dark');
        if (themeToggle) {
            themeToggle.checked = true;
        }
    }

    // Handle theme change on toggle
    if (themeToggle) {
        themeToggle.addEventListener('change', () => {
            if (themeToggle.checked) {
                docElement.classList.add('dark');
                localStorage.setItem('theme', 'dark');
                // Apply dark theme styles
                document.body.style.filter = 'brightness(0.8) contrast(1.2)';
            } else {
                docElement.classList.remove('dark');
                localStorage.setItem('theme', 'light');
                // Remove dark theme styles
                document.body.style.filter = '';
            }
        });
    }

    // --- Login Form Logic ---
    const loginForm = document.getElementById('login-form');
    const errorMessageDiv = document.getElementById('error-message');

    if (loginForm) {
        loginForm.addEventListener('submit', function(event) {
            event.preventDefault(); // Prevent actual form submission

            // Hide previous error messages
            errorMessageDiv.style.display = 'none';
            errorMessageDiv.textContent = '';
            errorMessageDiv.classList.remove('success');

            const userId = document.getElementById('user-id').value.trim();
            const password = document.getElementById('password').value.trim();

            // Clear any previous validation states
            const userIdField = document.getElementById('user-id').closest('.mdl-textfield');
            const passwordField = document.getElementById('password').closest('.mdl-textfield');

            if (userIdField) userIdField.classList.remove('is-invalid');
            if (passwordField) passwordField.classList.remove('is-invalid');

            // Basic validation - only check if fields are not empty
            if (!userId || !password) {
                errorMessageDiv.textContent = translations[currentLang].errorBothFields;
                errorMessageDiv.style.display = 'block';

                // Add validation states to Material Design fields
                if (!userId && userIdField) userIdField.classList.add('is-invalid');
                if (!password && passwordField) passwordField.classList.add('is-invalid');

                return;
            }

            // Show loading state
            const loginButton = loginForm.querySelector('.login-button');
            const originalText = loginButton.innerHTML;
            loginButton.innerHTML = '<i class="material-icons">hourglass_empty</i> Logging in...';
            loginButton.disabled = true;

            // --- Demo Login - Accept any credentials ---
            setTimeout(() => {
                errorMessageDiv.textContent = translations[currentLang].loginSuccess;
                errorMessageDiv.style.backgroundColor = 'rgba(76, 175, 80, 0.1)';
                errorMessageDiv.style.borderColor = 'rgba(76, 175, 80, 0.3)';
                errorMessageDiv.style.color = '#2e7d32';
                errorMessageDiv.classList.add('success');
                errorMessageDiv.style.display = 'block';

                // Reset button after a delay
                setTimeout(() => {
                    loginButton.innerHTML = originalText;
                    loginButton.disabled = false;
                    // Clear form for demo purposes
                    document.getElementById('user-id').value = '';
                    document.getElementById('password').value = '';
                    errorMessageDiv.style.display = 'none';

                    // Update Material Design textfields
                    if (userIdField && userIdField.MaterialTextfield) {
                        userIdField.MaterialTextfield.checkDirty();
                    }
                    if (passwordField && passwordField.MaterialTextfield) {
                        passwordField.MaterialTextfield.checkDirty();
                    }
                }, 2000);
            }, 1000);
        });
    }

    // Initialize language on page load
    setLanguage('en');

    // Ensure Material Design components are properly initialized
    setTimeout(() => {
        if (typeof componentHandler !== 'undefined') {
            componentHandler.upgradeDom();
        }
    }, 100);

});

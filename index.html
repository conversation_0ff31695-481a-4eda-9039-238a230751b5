<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDP Idea Tracking System - Login</title>

    <!-- Material Design Lite -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://code.getmdl.io/1.3.0/material.indigo-pink.min.css">

    <!-- Google Fonts: Roboto -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">

    <!-- Link to the external stylesheet -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>

    <div class="page-container">
        <div class="top-controls">
            <!-- Language Switcher -->
            <div class="language-switcher-container">
                <select id="language-switcher" class="language-switcher">
                    <option value="en">English</option>
                    <option value="es">Español</option>
                    <option value="de">Deutsch</option>
                </select>
                <i class="material-icons language-icon">language</i>
            </div>

            <!-- Theme Toggle -->
            <div class="theme-toggle-container">
                <label for="theme-toggle" class="mdl-switch mdl-js-switch mdl-js-ripple-effect theme-toggle-label">
                    <input type="checkbox" id="theme-toggle" class="mdl-switch__input">
                    <span class="mdl-switch__label">Dark Mode</span>
                </label>
            </div>
        </div>

        <!-- Header Content -->
        <header class="page-header">
            <h1 class="main-title" data-lang-key="mainTitle">CDP Idea Tracking System</h1>
            
        </header>

        <main class="main-content">
            <!-- Login Form Card -->
            <div class="login-card mdl-card mdl-shadow--8dp">
                <div class="mdl-card__title">
                    <h2 class="mdl-card__title-text form-title" data-lang-key="formTitle">Member Login</h2>
                </div>
                <div class="mdl-card__supporting-text">
                    <form id="login-form">
                        <!-- User ID Input -->
                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label">
                            <input class="mdl-textfield__input" type="text" id="user-id" name="user-id" required placeholder="Enter your user ID">
                            <label class="mdl-textfield__label" for="user-id" data-lang-key="userIdLabel">User ID</label>
                            <span class="mdl-textfield__error">User ID is required</span>
                        </div>

                        <!-- Password Input -->
                        <div class="mdl-textfield mdl-js-textfield mdl-textfield--floating-label">
                            <input class="mdl-textfield__input" type="password" id="password" name="password" required placeholder="Enter your password">
                            <label class="mdl-textfield__label" for="password" data-lang-key="passwordLabel">Password</label>
                            <span class="mdl-textfield__error">Password is required</span>
                        </div>

                        <!-- Remember Me & Forgot Password -->
                        <div class="form-options">
                            <label class="mdl-checkbox mdl-js-checkbox mdl-js-ripple-effect remember-me" for="remember-me">
                                <input type="checkbox" id="remember-me" name="remember-me" class="mdl-checkbox__input">
                                <span class="mdl-checkbox__label" data-lang-key="rememberMe">Remember me</span>
                            </label>
                            <div class="forgot-password">
                                <a href="#" class="mdl-button mdl-js-button mdl-button--accent" data-lang-key="forgotPassword">Forgot Password?</a>
                            </div>
                        </div>

                        <!-- Error Message Box -->
                        <div id="error-message" class="error-message"></div>
                    </form>
                </div>

                <!-- Login Button -->
                <div class="mdl-card__actions mdl-card--border">
                    <button type="submit" form="login-form" class="mdl-button mdl-js-button mdl-button--raised mdl-button--colored login-button" data-lang-key="loginButton">
                        <i class="material-icons">login</i>
                        Login
                    </button>
                </div>
            </div>

        </main>

        <!-- Footer -->
        <footer class="page-footer">
            <div class="footer-content">
                <div class="footer-left">
                    <p class="font-semibold" data-lang-key="helplineTitle">Helpline</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                    <p>1800-200-2600</p>
                </div>
                <div class="footer-right">
                    <p data-lang-key="copyright">Copyright © 2025 Quest Informatics Pvt Ltd. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Material Design Lite JavaScript -->
    <script defer src="https://code.getmdl.io/1.3.0/material.min.js"></script>

    <!-- Link to the external JavaScript file -->
    <script src="script.js"></script>
</body>
</html>
